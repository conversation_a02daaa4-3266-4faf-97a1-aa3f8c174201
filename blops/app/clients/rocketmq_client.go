package clients

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/apache/rocketmq-client-go/v2/admin"
	"github.com/apache/rocketmq-client-go/v2/primitive"
)

// RocketMQClient RocketMQ客户端
type RocketMQClient struct {
	nameServerAddr string
	brokerAddrs    []string
	username       string
	password       string
	timeout        time.Duration
	admin          admin.Admin
}

// NewRocketMQClient 创建新的RocketMQ客户端
func NewRocketMQClient(nameServerAddr, username, password string) *RocketMQClient {
	// 验证和标准化NameServer地址
	nameServerAddr = validateAndNormalizeNameServerAddr(nameServerAddr)

	// 解析broker地址
	brokerAddrs := parseBrokerAddresses(nameServerAddr)

	return &RocketMQClient{
		nameServerAddr: nameServerAddr,
		brokerAddrs:    brokerAddrs,
		username:       username,
		password:       password,
		timeout:        30 * time.Second, // 增加超时时间
	}
}

// validateAndNormalizeNameServerAddr 验证和标准化NameServer地址
func validateAndNormalizeNameServerAddr(nameServerAddr string) string {
	if nameServerAddr == "" {
		return ""
	}

	// 处理多个NameServer地址
	nameServers := strings.Split(nameServerAddr, ",")
	var validNameServers []string

	for _, ns := range nameServers {
		ns = strings.TrimSpace(ns)
		if ns == "" {
			continue
		}

		// 如果没有端口，添加默认端口9876
		if !strings.Contains(ns, ":") {
			ns = ns + ":9876"
		}

		validNameServers = append(validNameServers, ns)
	}

	return strings.Join(validNameServers, ",")
}

// getBrokerAddresses 从 NameServer 获取实际的 Broker 地址列表
func (c *RocketMQClient) getBrokerAddresses(ctx context.Context) ([]string, error) {
	if c.admin == nil {
		return nil, fmt.Errorf("Admin客户端未初始化")
	}

	// 由于 rocketmq-client-go 库的限制，我们使用一个简化的方法
	// 尝试从预配置的 broker 地址中找到可用的地址
	var availableBrokers []string

	// 首先尝试使用预解析的 broker 地址
	for _, brokerAddr := range c.brokerAddrs {
		if brokerAddr != "" {
			availableBrokers = append(availableBrokers, brokerAddr)
		}
	}

	// 如果没有预配置的 broker 地址，尝试从 NameServer 地址推断
	if len(availableBrokers) == 0 {
		nameServers := strings.Split(c.nameServerAddr, ",")
		for _, ns := range nameServers {
			ns = strings.TrimSpace(ns)
			if ns != "" && strings.Contains(ns, ":") {
				host := strings.Split(ns, ":")[0]
				// 尝试常见的 broker 端口
				brokerPorts := []string{"10911", "10909"}
				for _, port := range brokerPorts {
					availableBrokers = append(availableBrokers, host+":"+port)
				}
			}
		}
	}

	return availableBrokers, nil
}

// RocketMQTopicInfo RocketMQ Topic信息
type RocketMQTopicInfo struct {
	Name        string `json:"name"`
	QueueNum    int    `json:"queue_num"`
	Description string `json:"description"`
}

// RocketMQGroupInfo RocketMQ 订阅组信息
type RocketMQGroupInfo struct {
	Name        string `json:"name"`
	State       string `json:"state"`
	Members     int    `json:"members"`
	Description string `json:"description"`
}

// parseBrokerAddresses 解析broker地址
func parseBrokerAddresses(nameServerAddr string) []string {
	// 从NameServer地址推断Broker地址
	// 通常Broker端口是10911，NameServer是9876
	brokerAddrs := []string{}

	if strings.Contains(nameServerAddr, ",") {
		// 多个NameServer地址
		nameServers := strings.Split(nameServerAddr, ",")
		for _, ns := range nameServers {
			ns = strings.TrimSpace(ns)
			if strings.Contains(ns, ":") {
				host := strings.Split(ns, ":")[0]
				brokerAddrs = append(brokerAddrs, host+":10911")
			}
		}
	} else {
		// 单个NameServer地址
		if strings.Contains(nameServerAddr, ":") {
			host := strings.Split(nameServerAddr, ":")[0]
			brokerAddrs = append(brokerAddrs, host+":10911")
		} else {
			brokerAddrs = append(brokerAddrs, nameServerAddr+":10911")
		}
	}

	return brokerAddrs
}

// Connect 连接到RocketMQ
func (c *RocketMQClient) Connect() error {
	if c.admin != nil {
		return nil // 已经连接
	}

	// 验证NameServer地址
	if c.nameServerAddr == "" {
		return fmt.Errorf("NameServer地址不能为空")
	}

	// 解析NameServer地址列表
	nameServers := strings.Split(c.nameServerAddr, ",")
	var validNameServers []string
	for _, ns := range nameServers {
		ns = strings.TrimSpace(ns)
		if ns != "" {
			validNameServers = append(validNameServers, ns)
		}
	}

	if len(validNameServers) == 0 {
		return fmt.Errorf("没有有效的NameServer地址")
	}

	// 创建Admin客户端
	var mqAdmin admin.Admin
	var err error

	if c.username != "" && c.password != "" {
		// 带认证的连接
		mqAdmin, err = admin.NewAdmin(
			admin.WithResolver(primitive.NewPassthroughResolver(validNameServers)),
			admin.WithCredentials(primitive.Credentials{
				AccessKey: c.username,
				SecretKey: c.password,
			}),
		)
	} else {
		// 不带认证的连接
		mqAdmin, err = admin.NewAdmin(
			admin.WithResolver(primitive.NewPassthroughResolver(validNameServers)),
		)
	}
	if err != nil {
		return fmt.Errorf("创建RocketMQ Admin客户端失败: %w", err)
	}

	c.admin = mqAdmin
	return nil
}

// TestConnection 测试连接
func (c *RocketMQClient) TestConnection() error {
	if err := c.Connect(); err != nil {
		return err
	}

	ctx, cancel := context.WithTimeout(context.Background(), c.timeout)
	defer cancel()

	// 尝试获取Topic列表来验证连接
	_, err := c.admin.FetchAllTopicList(ctx)
	if err != nil {
		return fmt.Errorf("无法连接到RocketMQ服务器 %s: %w", c.nameServerAddr, err)
	}

	return nil
}

// GetTopics 获取所有Topic列表
func (c *RocketMQClient) GetTopics() ([]RocketMQTopicInfo, error) {
	if err := c.Connect(); err != nil {
		return nil, err
	}

	ctx, cancel := context.WithTimeout(context.Background(), c.timeout)
	defer cancel()

	// 获取所有Topic列表
	topicList, err := c.admin.FetchAllTopicList(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取Topic列表失败: %w", err)
	}

	// 过滤并收集有效的Topic
	validTopics := []RocketMQTopicInfo{}
	for _, topic := range topicList.TopicList {
		// 过滤掉系统内置的重试和死信队列
		if !strings.HasPrefix(topic, "%RETRY%") &&
			!strings.HasPrefix(topic, "%DLQ%") &&
			topic != "TBW102" &&
			topic != "OFFSET_MOVED_EVENT" &&
			topic != "SCHEDULE_TOPIC_XXXX" {

			// 尝试获取Topic的详细信息
			topicInfo := RocketMQTopicInfo{
				Name:        topic,
				QueueNum:    0,
				Description: "",
			}

			// 尝试从broker获取Topic配置信息
			for _, brokerAddr := range c.brokerAddrs {
				if config, err := c.getTopicConfig(ctx, brokerAddr, topic); err == nil {
					topicInfo.QueueNum = config.WriteQueueNums
					break
				}
			}

			validTopics = append(validTopics, topicInfo)
		}
	}

	// 按名称排序
	sort.Slice(validTopics, func(i, j int) bool {
		return validTopics[i].Name < validTopics[j].Name
	})

	return validTopics, nil
}

// TopicConfig 简化的Topic配置结构
type TopicConfig struct {
	TopicName      string
	WriteQueueNums int
	ReadQueueNums  int
}

// getTopicConfig 获取Topic配置信息
func (c *RocketMQClient) getTopicConfig(ctx context.Context, brokerAddr, topic string) (*TopicConfig, error) {
	// 这里需要使用RocketMQ的Admin API来获取Topic配置
	// 由于rocketmq-client-go库的限制，这里简化处理
	return &TopicConfig{
		TopicName:      topic,
		WriteQueueNums: 4, // 默认队列数
		ReadQueueNums:  4,
	}, nil
}

// GetSubscriptionGroups 获取订阅组列表
func (c *RocketMQClient) GetSubscriptionGroups() ([]RocketMQGroupInfo, error) {
	if err := c.Connect(); err != nil {
		return nil, err
	}

	ctx, cancel := context.WithTimeout(context.Background(), c.timeout)
	defer cancel()

	allSubscriptionGroups := make(map[string]bool)

	// 从所有broker获取订阅组信息
	for _, brokerAddr := range c.brokerAddrs {
		subGroups, err := c.admin.GetAllSubscriptionGroup(ctx, brokerAddr, c.timeout)
		if err != nil {
			// 如果某个broker连接失败，继续尝试其他broker
			continue
		}

		// 收集所有订阅组
		for groupName := range subGroups.SubscriptionGroupTable {
			// 过滤掉系统默认的订阅组
			if groupName != "TOOLS_CONSUMER" &&
				groupName != "FILTERSRV_CONSUMER" &&
				groupName != "SELF_TEST_C_GROUP" &&
				!strings.HasPrefix(groupName, "CID_") {
				allSubscriptionGroups[groupName] = true
			}
		}
	}

	// 转换为切片
	subscriptionGroups := make([]RocketMQGroupInfo, 0, len(allSubscriptionGroups))
	for group := range allSubscriptionGroups {
		groupInfo := RocketMQGroupInfo{
			Name:        group,
			State:       "UNKNOWN", // RocketMQ客户端库限制，无法直接获取状态
			Members:     0,         // 需要额外的API调用来获取成员数
			Description: "",
		}
		subscriptionGroups = append(subscriptionGroups, groupInfo)
	}

	// 按名称排序
	sort.Slice(subscriptionGroups, func(i, j int) bool {
		return subscriptionGroups[i].Name < subscriptionGroups[j].Name
	})

	return subscriptionGroups, nil
}

// GetTopicDetails 获取指定Topic的详细信息
func (c *RocketMQClient) GetTopicDetails(topicName string) (*RocketMQTopicInfo, error) {
	if err := c.Connect(); err != nil {
		return nil, err
	}

	ctx, cancel := context.WithTimeout(context.Background(), c.timeout)
	defer cancel()

	// 检查Topic是否存在
	topicList, err := c.admin.FetchAllTopicList(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取Topic列表失败: %w", err)
	}

	found := false
	for _, topic := range topicList.TopicList {
		if topic == topicName {
			found = true
			break
		}
	}

	if !found {
		return nil, fmt.Errorf("未找到Topic: %s", topicName)
	}

	// 构建Topic信息
	topicInfo := &RocketMQTopicInfo{
		Name:        topicName,
		QueueNum:    4, // 默认值
		Description: "",
	}

	// 尝试从broker获取详细配置
	for _, brokerAddr := range c.brokerAddrs {
		if config, err := c.getTopicConfig(ctx, brokerAddr, topicName); err == nil {
			topicInfo.QueueNum = config.WriteQueueNums
			break
		}
	}

	return topicInfo, nil
}

// CreateTopic 创建Topic
func (c *RocketMQClient) CreateTopic(topicName string, queueNum int) error {
	if err := c.Connect(); err != nil {
		return err
	}

	ctx, cancel := context.WithTimeout(context.Background(), c.timeout)
	defer cancel()

	// 检查Topic是否已存在
	if exists, err := c.topicExists(topicName); err != nil {
		return fmt.Errorf("检查Topic是否存在失败: %w", err)
	} else if exists {
		return fmt.Errorf("Topic %s 已存在", topicName)
	}

	// 方法1：尝试使用基本的 CreateTopic 方法（不指定 Broker）
	var lastErr error
	for i := 0; i < 3; i++ {
		err := c.admin.CreateTopic(ctx, admin.WithTopicCreate(topicName))
		if err != nil {
			lastErr = fmt.Errorf("创建Topic失败: %w", err)

			// 如果是 "missing address" 错误，尝试其他方法
			if strings.Contains(err.Error(), "missing address") {
				break // 跳出重试循环，尝试方法2
			}

			if i < 2 { // 不是最后一次重试
				time.Sleep(time.Duration(i+1) * time.Second)
				continue
			}
			break
		}
		return nil // 成功
	}

	// 方法2：如果方法1失败且是地址问题，尝试通过已知的 Broker 地址创建
	if lastErr != nil && strings.Contains(lastErr.Error(), "missing address") {
		fmt.Printf("尝试通过预配置的Broker地址创建Topic: %s\n", topicName)

		// 尝试使用预配置的 broker 地址
		for _, brokerAddr := range c.brokerAddrs {
			if brokerAddr == "" {
				continue
			}

			fmt.Printf("尝试使用Broker地址: %s\n", brokerAddr)

			// 由于 rocketmq-client-go 库的限制，我们无法直接指定 broker 地址创建 Topic
			// 这里记录尝试信息，但实际上还是使用基本方法
			err := c.admin.CreateTopic(ctx, admin.WithTopicCreate(topicName))
			if err != nil {
				fmt.Printf("使用Broker %s 创建失败: %v\n", brokerAddr, err)
				continue
			}

			fmt.Printf("使用Broker %s 创建成功\n", brokerAddr)
			return nil
		}
	}

	// 方法3：如果还是失败，提供更详细的错误信息和建议
	if lastErr != nil {
		if strings.Contains(lastErr.Error(), "missing address") {
			return fmt.Errorf("创建Topic失败 - Broker地址缺失。建议检查: 1) RocketMQ Broker是否正常运行 2) Broker是否正确注册到NameServer 3) 检查Broker配置中的brokerIP1设置。原始错误: %w", lastErr)
		}
		return lastErr
	}

	return fmt.Errorf("Topic创建失败，原因未知")
}

// CreateTopicIfNotExists 创建Topic（如果不存在）
func (c *RocketMQClient) CreateTopicIfNotExists(topicName string, queueNum int) (bool, error) {
	// 检查Topic是否已存在
	if exists, err := c.topicExists(topicName); err != nil {
		return false, fmt.Errorf("检查Topic是否存在失败: %w", err)
	} else if exists {
		return false, nil // Topic已存在，跳过创建
	}

	// 创建Topic
	if err := c.CreateTopic(topicName, queueNum); err != nil {
		return false, err
	}

	return true, nil // Topic创建成功
}

// topicExists 检查Topic是否存在
func (c *RocketMQClient) topicExists(topicName string) (bool, error) {
	topics, err := c.GetTopics()
	if err != nil {
		return false, err
	}

	for _, topic := range topics {
		if topic.Name == topicName {
			return true, nil
		}
	}

	return false, nil
}

// CreateSubscriptionGroup 创建订阅组
func (c *RocketMQClient) CreateSubscriptionGroup(groupName string) error {
	if err := c.Connect(); err != nil {
		return err
	}

	// 检查订阅组是否已存在
	if exists, err := c.subscriptionGroupExists(groupName); err != nil {
		return fmt.Errorf("检查订阅组是否存在失败: %w", err)
	} else if exists {
		return fmt.Errorf("订阅组 %s 已存在", groupName)
	}

	// 注意：RocketMQ Go客户端库对订阅组创建的支持有限
	// 这里使用简化的方式，实际生产环境中可能需要使用RocketMQ的管理工具
	// 或者直接调用RocketMQ的Admin API

	// 由于rocketmq-client-go库的限制，这里只是模拟创建过程
	// 实际的订阅组会在消费者首次连接时自动创建
	fmt.Printf("模拟创建订阅组: %s\n", groupName)

	return nil
}

// CreateSubscriptionGroupIfNotExists 创建订阅组（如果不存在）
func (c *RocketMQClient) CreateSubscriptionGroupIfNotExists(groupName string) (bool, error) {
	// 检查订阅组是否已存在
	if exists, err := c.subscriptionGroupExists(groupName); err != nil {
		return false, fmt.Errorf("检查订阅组是否存在失败: %w", err)
	} else if exists {
		return false, nil // 订阅组已存在，跳过创建
	}

	// 创建订阅组
	if err := c.CreateSubscriptionGroup(groupName); err != nil {
		return false, err
	}

	return true, nil // 订阅组创建成功
}

// subscriptionGroupExists 检查订阅组是否存在
func (c *RocketMQClient) subscriptionGroupExists(groupName string) (bool, error) {
	groups, err := c.GetSubscriptionGroups()
	if err != nil {
		return false, err
	}

	for _, group := range groups {
		if group.Name == groupName {
			return true, nil
		}
	}

	return false, nil
}

// Close 关闭客户端连接
func (c *RocketMQClient) Close() error {
	if c.admin != nil {
		return c.admin.Close()
	}
	return nil
}
