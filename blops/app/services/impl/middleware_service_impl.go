package impl

import (
	"blops/app/clients"
	"blops/app/models"
	"blops/app/models/dto"
	"blops/app/models/po"
	"fmt"
	"strings"
	"time"
)

// MiddlewareService 中间件服务实现
type MiddlewareService struct {
	envModel         *models.MiddlewareEnvMgr
	linkModel        *models.MiddlewareLinkMgr
	topicConfigModel *models.TopicConfigMgr
	topicCacheModel  *models.TopicCacheMgr
	groupCacheModel  *models.GroupCacheMgr
	cacheMetaModel   *models.TopicGroupCacheMetaMgr
}

// 确保 MiddlewareService 实现了 MiddlewareServiceInf 接口
var MiddlewareSvc *MiddlewareService

func init() {
	MiddlewareSvc = &MiddlewareService{
		envModel:         models.NewMiddlewareEnvMgr(),
		linkModel:        models.NewMiddlewareLinkMgr(),
		topicConfigModel: models.NewTopicConfigMgr(),
		topicCacheModel:  models.NewTopicCacheMgr(),
		groupCacheModel:  models.NewGroupCacheMgr(),
		cacheMetaModel:   models.NewTopicGroupCacheMetaMgr(),
	}
}

// =============== 环境相关方法 ===============

// GetEnv 获取单个环境
func (svc *MiddlewareService) GetEnv(id int64) (*po.MiddlewareEnv, error) {
	env, err := svc.envModel.Get(id)
	if err != nil {
		return nil, err
	}
	return &env, nil
}

// ListEnvs 获取所有环境
func (svc *MiddlewareService) ListEnvs() ([]*po.MiddlewareEnv, error) {
	return svc.envModel.Gets()
}

// CreateEnv 创建环境
func (svc *MiddlewareService) CreateEnv(env *po.MiddlewareEnv) error {
	env.CreatedAt = time.Now()
	return svc.envModel.Create(env)
}

// UpdateEnv 更新环境
func (svc *MiddlewareService) UpdateEnv(env *po.MiddlewareEnv) error {
	return svc.envModel.Update(env)
}

// DeleteEnv 删除环境
func (svc *MiddlewareService) DeleteEnv(id int64) error {
	return svc.envModel.Delete(id)
}

// =============== 链接相关方法 ===============

// GetLink 获取单个链接
func (svc *MiddlewareService) GetLink(id int64) (*po.MiddlewareLink, error) {
	link, err := svc.linkModel.Get(id)
	if err != nil {
		return nil, err
	}
	return &link, nil
}

// ListLinks 获取所有链接
func (svc *MiddlewareService) ListLinks() ([]*po.MiddlewareLink, error) {
	return svc.linkModel.Gets()
}

// ListLinksByType 根据类型获取链接
func (svc *MiddlewareService) ListLinksByType(linkType string) ([]*po.MiddlewareLink, error) {
	return svc.linkModel.GetsByType(linkType)
}

// ListLinksByEnv 根据环境ID获取链接
func (svc *MiddlewareService) ListLinksByEnv(envID int64) ([]*po.MiddlewareLink, error) {
	return svc.linkModel.GetsByEnv(envID)
}

// ListLinksByTypeAndEnv 根据类型和环境ID获取链接
func (svc *MiddlewareService) ListLinksByTypeAndEnv(linkType string, envID int64) ([]*po.MiddlewareLink, error) {
	return svc.linkModel.GetsByTypeAndEnv(linkType, envID)
}

// ListLinksWithEnvInfo 获取所有链接并包含环境信息
func (svc *MiddlewareService) ListLinksWithEnvInfo() ([]*po.MiddlewareLink, error) {
	return svc.linkModel.GetsWithEnvInfo()
}

// ListLinksByTypeWithEnvInfo 根据类型获取链接并包含环境信息
func (svc *MiddlewareService) ListLinksByTypeWithEnvInfo(linkType string) ([]*po.MiddlewareLink, error) {
	return svc.linkModel.GetsByTypeWithEnvInfo(linkType)
}

// CreateLink 创建链接
func (svc *MiddlewareService) CreateLink(link *po.MiddlewareLink) error {
	link.CreatedAt = time.Now()
	return svc.linkModel.Create(link)
}

// UpdateLink 更新链接
func (svc *MiddlewareService) UpdateLink(link *po.MiddlewareLink) error {
	return svc.linkModel.Update(link)
}

// DeleteLink 删除链接
func (svc *MiddlewareService) DeleteLink(id int64) error {
	return svc.linkModel.Delete(id)
}

// =============== Topic配置相关方法 ===============

// GetTopicConfig 获取单个Topic配置
func (svc *MiddlewareService) GetTopicConfig(id int64) (*po.TopicConfig, error) {
	config, err := svc.topicConfigModel.Get(id)
	if err != nil {
		return nil, err
	}
	return &config, nil
}

// ListTopicConfigs 获取所有Topic配置
func (svc *MiddlewareService) ListTopicConfigs() ([]*po.TopicConfig, error) {
	return svc.topicConfigModel.Gets()
}

// ListTopicConfigsByType 根据类型获取Topic配置
func (svc *MiddlewareService) ListTopicConfigsByType(configType string) ([]*po.TopicConfig, error) {
	return svc.topicConfigModel.GetsByType(configType)
}

// ListTopicConfigsByEnv 根据环境ID获取Topic配置
func (svc *MiddlewareService) ListTopicConfigsByEnv(envID int64) ([]*po.TopicConfig, error) {
	return svc.topicConfigModel.GetsByEnv(envID)
}

// ListTopicConfigsByTypeAndEnv 根据类型和环境ID获取Topic配置
func (svc *MiddlewareService) ListTopicConfigsByTypeAndEnv(configType string, envID int64) ([]*po.TopicConfig, error) {
	return svc.topicConfigModel.GetsByTypeAndEnv(configType, envID)
}

// ListTopicConfigsWithEnvInfo 获取所有Topic配置并包含环境信息
func (svc *MiddlewareService) ListTopicConfigsWithEnvInfo() ([]*po.TopicConfig, error) {
	return svc.topicConfigModel.GetsWithEnvInfo()
}

// ListTopicConfigsByTypeWithEnvInfo 根据类型获取Topic配置并包含环境信息
func (svc *MiddlewareService) ListTopicConfigsByTypeWithEnvInfo(configType string) ([]*po.TopicConfig, error) {
	return svc.topicConfigModel.GetsByTypeWithEnvInfo(configType)
}

// CreateTopicConfig 创建Topic配置
func (svc *MiddlewareService) CreateTopicConfig(config *po.TopicConfig) error {
	config.CreatedAt = time.Now()
	return svc.topicConfigModel.Create(config)
}

// UpdateTopicConfig 更新Topic配置
func (svc *MiddlewareService) UpdateTopicConfig(config *po.TopicConfig) error {
	return svc.topicConfigModel.Update(config)
}

// DeleteTopicConfig 删除Topic配置
func (svc *MiddlewareService) DeleteTopicConfig(id int64) error {
	return svc.topicConfigModel.Delete(id)
}

// GetTopicsAndGroups 获取Topic和Group信息（优先从缓存获取）
func (svc *MiddlewareService) GetTopicsAndGroups(configID int64) (*dto.TopicListResponse, error) {
	// 首先尝试从缓存获取
	cachedResult, err := svc.GetTopicsAndGroupsFromCache(configID)
	if err == nil && cachedResult != nil {
		return cachedResult, nil
	}

	// 缓存中没有数据，从实时源获取并缓存
	return svc.RefreshTopicsAndGroupsCache(configID)
}

// GetTopicsAndGroupsFromCache 从缓存获取Topic和Group信息
func (svc *MiddlewareService) GetTopicsAndGroupsFromCache(configID int64) (*dto.TopicListResponse, error) {
	// 检查缓存元数据
	meta, err := svc.cacheMetaModel.GetByConfigID(configID)
	if err != nil {
		return nil, fmt.Errorf("缓存元数据不存在: %v", err)
	}

	// 检查同步状态
	if meta.SyncStatus != "success" {
		return nil, fmt.Errorf("缓存数据状态异常: %s", meta.SyncStatus)
	}

	// 获取Topic缓存
	topicCaches, err := svc.topicCacheModel.GetsByConfigID(configID)
	if err != nil {
		return nil, fmt.Errorf("获取Topic缓存失败: %v", err)
	}

	// 获取Group缓存
	groupCaches, err := svc.groupCacheModel.GetsByConfigID(configID)
	if err != nil {
		return nil, fmt.Errorf("获取Group缓存失败: %v", err)
	}

	// 转换为DTO格式
	topics := make([]dto.TopicInfo, 0, len(topicCaches))
	for _, cache := range topicCaches {
		topics = append(topics, dto.TopicInfo{
			Name:        cache.Name,
			Partitions:  cache.Partitions,
			Replicas:    cache.Replicas,
			Description: cache.Description,
		})
	}

	groups := make([]dto.GroupInfo, 0, len(groupCaches))
	for _, cache := range groupCaches {
		groups = append(groups, dto.GroupInfo{
			Name:        cache.Name,
			State:       cache.State,
			Members:     cache.Members,
			Description: cache.Description,
		})
	}

	return &dto.TopicListResponse{
		Topics: topics,
		Groups: groups,
	}, nil
}

// RefreshTopicsAndGroupsCache 刷新Topic和Group缓存
func (svc *MiddlewareService) RefreshTopicsAndGroupsCache(configID int64) (*dto.TopicListResponse, error) {
	// 获取配置信息
	config, err := svc.GetTopicConfig(configID)
	if err != nil {
		return nil, fmt.Errorf("获取配置失败: %v", err)
	}

	// 更新缓存元数据状态为同步中
	meta := &po.TopicGroupCacheMeta{
		ConfigID:   configID,
		SyncStatus: "syncing",
		UpdatedAt:  time.Now(),
	}
	svc.cacheMetaModel.CreateOrUpdate(meta)

	// 根据配置类型调用不同的客户端获取实时数据
	var result *dto.TopicListResponse
	switch config.Type {
	case "kafka":
		result, err = svc.getKafkaTopicsAndGroups(config)
	case "rocketmq":
		result, err = svc.getRocketMQTopicsAndGroups(config)
	default:
		err = fmt.Errorf("不支持的配置类型: %s", config.Type)
	}

	if err != nil {
		// 更新缓存元数据状态为失败
		meta.SyncStatus = "failed"
		meta.ErrorMessage = err.Error()
		meta.UpdatedAt = time.Now()
		svc.cacheMetaModel.CreateOrUpdate(meta)
		return nil, err
	}

	// 清除旧的缓存数据
	svc.topicCacheModel.DeleteByConfigID(configID)
	svc.groupCacheModel.DeleteByConfigID(configID)

	// 保存新的Topic缓存
	if len(result.Topics) > 0 {
		topicCaches := make([]*po.TopicCache, 0, len(result.Topics))
		for _, topic := range result.Topics {
			topicCaches = append(topicCaches, &po.TopicCache{
				ConfigID:    configID,
				Name:        topic.Name,
				Partitions:  topic.Partitions,
				Replicas:    topic.Replicas,
				Description: topic.Description,
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}
		if err := svc.topicCacheModel.BatchCreate(topicCaches); err != nil {
			return nil, fmt.Errorf("保存Topic缓存失败: %v", err)
		}
	}

	// 保存新的Group缓存
	if len(result.Groups) > 0 {
		groupCaches := make([]*po.GroupCache, 0, len(result.Groups))
		for _, group := range result.Groups {
			groupCaches = append(groupCaches, &po.GroupCache{
				ConfigID:    configID,
				Name:        group.Name,
				State:       group.State,
				Members:     group.Members,
				Description: group.Description,
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}
		if err := svc.groupCacheModel.BatchCreate(groupCaches); err != nil {
			return nil, fmt.Errorf("保存Group缓存失败: %v", err)
		}
	}

	// 更新缓存元数据状态为成功
	meta.SyncStatus = "success"
	meta.LastSyncAt = time.Now()
	meta.TopicCount = len(result.Topics)
	meta.GroupCount = len(result.Groups)
	meta.ErrorMessage = ""
	meta.UpdatedAt = time.Now()
	svc.cacheMetaModel.CreateOrUpdate(meta)

	return result, nil
}

// GetCacheMeta 获取缓存元数据
func (svc *MiddlewareService) GetCacheMeta(configID int64) (*po.TopicGroupCacheMeta, error) {
	return svc.cacheMetaModel.GetByConfigID(configID)
}

// UpdateCacheMeta 更新缓存元数据
func (svc *MiddlewareService) UpdateCacheMeta(meta *po.TopicGroupCacheMeta) error {
	return svc.cacheMetaModel.CreateOrUpdate(meta)
}

// ClearCache 清除缓存
func (svc *MiddlewareService) ClearCache(configID int64) error {
	// 删除Topic缓存
	if err := svc.topicCacheModel.DeleteByConfigID(configID); err != nil {
		return fmt.Errorf("删除Topic缓存失败: %v", err)
	}

	// 删除Group缓存
	if err := svc.groupCacheModel.DeleteByConfigID(configID); err != nil {
		return fmt.Errorf("删除Group缓存失败: %v", err)
	}

	// 删除缓存元数据
	if err := svc.cacheMetaModel.DeleteByConfigID(configID); err != nil {
		return fmt.Errorf("删除缓存元数据失败: %v", err)
	}

	return nil
}

// getKafkaTopicsAndGroups 获取Kafka的Topic和Group信息
func (svc *MiddlewareService) getKafkaTopicsAndGroups(config *po.TopicConfig) (*dto.TopicListResponse, error) {
	// 创建新的Kafka客户端（使用sarama库）
	kafkaClient := clients.NewKafkaSaramaClient(config.Address, config.Username, config.Password)
	defer kafkaClient.Close()

	// 测试连接
	if err := kafkaClient.TestConnection(); err != nil {
		return nil, fmt.Errorf("Kafka连接测试失败: %v", err)
	}

	// 获取Topic列表
	kafkaTopics, err := kafkaClient.GetTopics()
	if err != nil {
		return nil, fmt.Errorf("获取Kafka Topic列表失败: %v", err)
	}

	// 转换为DTO格式
	topics := make([]dto.TopicInfo, 0, len(kafkaTopics))
	for _, topic := range kafkaTopics {
		topics = append(topics, dto.TopicInfo{
			Name:        topic.Name,
			Partitions:  topic.Partitions,
			Replicas:    topic.Replicas,
			Description: fmt.Sprintf("Kafka Topic - %d分区, %d副本", topic.Partitions, topic.Replicas),
		})
	}

	// 获取消费者组信息（使用sarama库）
	groups := []dto.GroupInfo{}
	kafkaConsumerGroups, err := kafkaClient.GetConsumerGroups()
	if err != nil {
		// 如果获取失败，添加错误信息
		groups = append(groups, dto.GroupInfo{
			Name:        "消费者组查询",
			State:       "Error",
			Members:     0,
			Description: fmt.Sprintf("获取消费者组失败: %v", err),
		})
	} else if len(kafkaConsumerGroups) == 0 {
		// 如果没有消费者组
		groups = append(groups, dto.GroupInfo{
			Name:        "无消费者组",
			State:       "Empty",
			Members:     0,
			Description: "当前没有活动的消费者组",
		})
	} else {
		// 正常获取到消费者组
		for _, group := range kafkaConsumerGroups {
			// 构建订阅的Topic描述
			topicsDesc := ""
			if len(group.Topics) > 0 {
				topicsDesc = strings.Join(group.Topics, ", ")
				if len(topicsDesc) > 50 {
					topicsDesc = topicsDesc[:47] + "..."
				}
			}
			
			description := fmt.Sprintf("Kafka消费者组 - %d个成员订阅了%d个Topic", group.Members, len(group.Topics))
			
			groups = append(groups, dto.GroupInfo{
				Name:        group.Name,
				State:       group.State,
				Members:     group.Members,
				Description: description,
			})
		}
	}

	return &dto.TopicListResponse{
		Topics: topics,
		Groups: groups,
	}, nil
}

// getRocketMQTopicsAndGroups 获取RocketMQ的Topic和Group信息
func (svc *MiddlewareService) getRocketMQTopicsAndGroups(config *po.TopicConfig) (*dto.TopicListResponse, error) {
	// 创建RocketMQ客户端
	rocketMQClient := clients.NewRocketMQClient(config.Address, config.Username, config.Password)
	defer rocketMQClient.Close()

	// 测试连接
	if err := rocketMQClient.TestConnection(); err != nil {
		return nil, fmt.Errorf("RocketMQ连接测试失败: %v", err)
	}

	// 获取Topic列表
	rocketMQTopics, err := rocketMQClient.GetTopics()
	if err != nil {
		return nil, fmt.Errorf("获取RocketMQ Topic列表失败: %v", err)
	}

	// 转换为DTO格式
	topics := make([]dto.TopicInfo, 0, len(rocketMQTopics))
	for _, topic := range rocketMQTopics {
		topics = append(topics, dto.TopicInfo{
			Name:        topic.Name,
			Partitions:  topic.QueueNum,
			Replicas:    0, // RocketMQ没有副本概念
			Description: fmt.Sprintf("RocketMQ Topic - %d队列", topic.QueueNum),
		})
	}

	// 获取订阅组信息
	groups := []dto.GroupInfo{}
	subscriptionGroups, err := rocketMQClient.GetSubscriptionGroups()
	if err == nil {
		for _, group := range subscriptionGroups {
			groups = append(groups, dto.GroupInfo{
				Name:        group.Name,
				State:       group.State,
				Members:     group.Members,
				Description: "RocketMQ订阅组",
			})
		}
	}

	return &dto.TopicListResponse{
		Topics: topics,
		Groups: groups,
	}, nil
}
